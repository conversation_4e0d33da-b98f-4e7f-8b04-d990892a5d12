import csv
import json
import os
from pathlib import Path
from tempfile import gettempdir
from typing import Any, Async<PERSON><PERSON>ator, Callable, Dict, List, Optional
from uuid import uuid4

import decouple
from fastapi import HTTPException
from openai import APIError, AsyncOpenAI, AuthenticationError, RateLimitError
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.workbook import Workbook

from app.src.exceptions.error_code import ChatbotErrorCode
from app.src.schemas.chat_sessions import ChatHistoryItem
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Search the web for current information related to companies, products, industry trends, or social buzz in Japan. Used to find advertising context, homepage content, or what is currently trending in the market.",            "parameters": {"type": "object", "properties": {"query": {"type": "string"}}, "required": ["query"]},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "create_csv_file",
            "description": "Creates a downloadable CSV file from the given content.",
            "parameters": {
                "type": "object",
                "properties": {
                    "content": {
                        "type": "string",
                        "description": "The content to store in CSV, plain text, line by line or comma separated.",
                    }
                },
                "required": ["content"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "create_excel_file",
            "description": "Creates a downloadable Excel file for an advertising campaign based on the specified platform.",
            "parameters": {
                "type": "object",
                "properties": {
                    "platform": {
                        "type": "string",
                        "description": "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max).",
                        "enum": [
                            "LINE Ads",
                            "Meta (Instagram/Facebook)",
                            "YouTube Ads",
                            "Google Search Ads",
                            "Google Display Ads",
                            "Google Demand Gen Ads",
                            "P-Max"
                        ]
                    }
                },
                "required": ["platform"]
            },
        },
    },
]

MODEL = decouple.config("MODEL")


def get_field_value(field_data, default=""):
    if isinstance(field_data, dict) and "value" in field_data:
        return field_data.get("value", default)
    return field_data if field_data is not None else default


def set_cell(ws, row, col, value="", fill=None, bold=False, merge_end_col=None, merge_end_row=None):
    end_row = merge_end_row or row
    end_col = merge_end_col or col

    if merge_end_col:
        ws.merge_cells(start_row=row, start_column=col, end_row=row, end_column=merge_end_col)
    if merge_end_row:
        ws.merge_cells(start_row=row, start_column=col, end_row=merge_end_row, end_column=col)
    thin_border = Border(
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000")
    )

    cell = ws.cell(row=row, column=col, value=value)
    cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    cell.border = thin_border
    if fill:
        cell.fill = fill
    if bold:
        cell.font = Font(bold=True)

    for r in range(row, end_row + 1):
        for c in range(col, end_col + 1):
            if r == row and c == col:
                continue
            temp_cell = ws.cell(row=r, column=c)
            temp_cell.border = thin_border

    return cell


class LLMService:
    def __init__(self, search_service: Optional[SearchService] = None, prompt_service: Optional[PromptService] = None):
        self.openai_client = AsyncOpenAI(api_key=decouple.config("OPENAI_API_KEY", default=None))
        self.search_service = search_service
        self.prompt_service = prompt_service
        self.outputs_dir = Path(__file__).parent.parent / "data/outputs"

    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        try:
            system_prompt = await self.prompt_service._get_prompts()
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        # print("System prompt: {}".format(system_prompt))
        messages = [{"role": "system", "content": system_prompt}]

        for item in chat_history:
            messages.append({"role": item.role, "content": item.content})
        messages.append({"role": "user", "content": request.question})

        try:
            return self._execute_stream(messages, max_tokens, temperature, on_tool_call)

        except APIError:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value
        except RateLimitError:
            raise ChatbotErrorCode.REQUEST_DENIED.value
        except AuthenticationError:
            raise ChatbotErrorCode.AUTHENTICATION_FAILED.value
        except Exception:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value

    async def _execute_stream(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        error = False
        response = await self.openai_client.chat.completions.create(
            model=MODEL,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            tools=TOOLS,
            tool_choice="auto",
            stream=True,
        )

        tool_calls = []
        streaming_content = ""

        async for chunk in response:
            choice = chunk.choices[0]
            delta = choice.delta

            if delta.content:
                streaming_content += delta.content
                yield delta.content

            if delta.tool_calls:
                for tcchunk in delta.tool_calls:
                    index = tcchunk.index
                    while len(tool_calls) <= index:
                        tool_calls.append(
                            {
                                "id": "",
                                "type": "function",
                                "function": {"name": "", "arguments": ""},
                            }
                        )

                    tool_call = tool_calls[index]
                    tool_call["id"] += tcchunk.id or ""
                    tool_call["function"]["name"] += tcchunk.function.name or ""
                    tool_call["function"]["arguments"] += tcchunk.function.arguments or ""

            if choice.finish_reason in {"stop", "tool_calls"}:
                break

        if tool_calls:
            messages.append({"role": "assistant", "tool_calls": tool_calls})
            for tool_call in tool_calls:
                tool_name = tool_call["function"]["name"]
                if on_tool_call:
                    await on_tool_call(tool_name)

                if tool_call["function"]["name"] == "web_search":
                    args = json.loads(tool_call["function"]["arguments"])
                    search_result = await self.search_service.search(args["query"])

                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": "web_search",
                            "content": json.dumps(search_result, ensure_ascii=False),
                        }
                    )

                elif tool_name == "create_excel_file":
                    args = json.loads(tool_call["function"]["arguments"])
                    platform = args["platform"]

                    platform_mapping = {
                        "LINE Ads": "line_ads_format.json",
                        "Meta (Instagram/Facebook)": "meta_ads_format.json",
                        "YouTube Ads": "youtube_ads_format.json",
                        "Google Search Ads": "google_search_ads_format.json",
                        "Google Display Ads": "google_display_ads_format.json",
                        "Google Demand Gen Ads": "google_demand_gen_ads_format.json",
                        "P-Max": "p_max_format.json"
                    }
                    format_file = platform_mapping.get(platform)
                    if not format_file:
                        error = True
                        async for chunk in self._stream_gpt_error_response(
                            error_text=f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads",
                            max_tokens=max_tokens,
                            temperature=temperature
                        ):
                            yield chunk
                        return

                    output_format = await self._load_output_format(format_file)

                    schema_fields = output_format.get("fields", {})

                    schema_prompt = self.generate_prompt_from_output_format(platform=platform, fields=schema_fields)

                    system_prompt = await self.prompt_service._get_prompts()
                    system_prompt += (
                        f"\n\n{schema_prompt}\n"
                        "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                        "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                        "Strictly return only a valid JSON object using this exact format:\n\n"
                        "{\n  \"data\": { ... },\n  \"missing_fields\": [ ... ]\n}\n\n"
                        "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
                    )

                    print(f"\n📥 Generated system_prompt:\n{system_prompt}\n")
                    new_messages = [
                        {"role": "system", "content": system_prompt},
                        *messages[1:-1],
                    ]

                    extraction_response = await self.openai_client.chat.completions.create(
                        model=MODEL,
                        messages=new_messages,
                        max_tokens=max_tokens,
                        temperature=temperature
                    )
                    try:
                        campaign_data = json.loads(extraction_response.choices[0].message.content)
                    except json.JSONDecodeError:
                        error = True
                        async for chunk in self._stream_gpt_error_response(
                            "❌ JSONの解析に失敗しました。もう一度お試しください。",
                            max_tokens=max_tokens,
                            temperature=temperature
                        ):
                            yield chunk
                        return
                    if "error" in campaign_data:
                        error = True
                        async for chunk in self._stream_gpt_error_response(
                            "❌ プラットフォームが不明です。対応している広告プラットフォームを指定してください。",
                            max_tokens=max_tokens,
                            temperature=temperature
                        ):
                            yield chunk
                        return

                    filename = f"gen_excel_{uuid4().hex}.xlsx"
                    file_path = os.path.join(gettempdir(), filename)
                    try:
                        platform_write_functions = {
                            "Meta (Instagram/Facebook)": self._write_excel_file_meta,
                            "LINE Ads": self._write_excel_file_line,
                            "YouTube Ads": self._write_excel_file_youtube,
                            "Google Search Ads": self._write_excel_file_google_search,
                            "Google Display Ads": self._write_excel_file_google_display,
                            "Google Demand Gen Ads": self._write_excel_file_google_demand_gen,
                            "P-Max": self._write_excel_file_pmax,
                            "Ad Extensions": self._write_excel_file_ad_extensions,
                        }

                        writer_func = platform_write_functions.get(platform)
                        if not writer_func:
                            raise ValueError(f"No Excel writer defined for platform: {platform}")

                        await writer_func(campaign_data, output_format, file_path)
                        download_url = f"/be/chat/download-excel?filename={filename}"
                        messages.append(
                            {
                                "role": "tool",
                                "tool_call_id": tool_call["id"],
                                "name": "create_excel_file",
                                "content": json.dumps({"download_url": download_url}),
                            }
                        )
                    except Exception as e:
                        error = True
                        print("Error: ", e)
                        async for chunk in self._stream_gpt_error_response(
                            error_text=f"❌ Excelファイル作成中にエラーが発生しました",
                            max_tokens=max_tokens,
                            temperature=temperature
                        ):
                            yield chunk

                elif tool_call["function"]["name"] == "create_csv_file":
                    args = json.loads(tool_call["function"]["arguments"])
                    csv_text = args["content"]

                    filename = f"gen_csv_{uuid4().hex}.csv"
                    file_path = os.path.join(gettempdir(), filename)

                    with open(file_path, "w", encoding="utf-8", newline="") as f:
                        writer = csv.writer(f)
                        for line in csv_text.strip().split("\n"):
                            writer.writerow([cell.strip() for cell in line.split(",")])

                    download_url = f"/be/chat/download-csv?filename={filename}"

                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": "create_csv_file",
                            "content": json.dumps({"download_url": download_url}),
                        }
                    )
            if not error:
                followup_response = await self.openai_client.chat.completions.create(
                    model=MODEL,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=True,
                )

                async for chunk in followup_response:
                    if chunk.choices and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content

    async def _stream_gpt_error_response(self, error_text, temperature, max_tokens):
        prompt = [
            {
                "role": "system",
                "content": "あなたはプロフェッショナルな日本人の広告コンサルタントです。丁寧かつ親切な口調で、ユーザーにエラーや不足情報について優しく説明してください。"
            },
            {
                "role": "user",
                "content": error_text.strip()
            }
        ]

        response = await self.openai_client.chat.completions.create(
            model=MODEL,
            messages=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=True
        )

        async for chunk in response:
            if chunk.choices and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    def generate_prompt_from_output_format(self, platform: str, fields: dict) -> str:
        def format_field_line(field_name: str, rules: dict, indent: int = 0) -> str:
            space = " " * indent
            # Start with the field name
            description = f'{space}- "{field_name}": '

            # Add the description from the JSON if it exists
            if "description" in rules:
                description += f"{rules['description']} — "

            # Add the format/type information
            if "options" in rules and "max_count" in rules:
                description += f"List of {rules['max_count']} items and each value is One of {rules['options']}"
            elif "options" in rules:
                description += f"One of {rules['options']}"
            elif "max_count" in rules and isinstance(rules.get("max_count"), int):
                max_chars = rules.get("max_chars")
                item_info = f"each ≤ {max_chars} chars" if max_chars else ""
                description += f"List of {rules['max_count']} items ({item_info})"
            elif "max_chars" in rules:
                description += f"String (max {rules['max_chars']} chars)"
            else:
                description += "String"

            # Add "Required" if applicable
            if rules.get("required"):
                description += " — Required"

            return description

        def parse_fields(schema: dict, indent: int = 0) -> list:
            lines = []
            for field_name, rule in schema.items():
                if isinstance(rule, dict) and "cards" in rule:
                    lines.append(
                        "  " * indent + f'- "{field_name}": Object with cards (max_count: {rule.get("max_count", 4)}):')
                    for subfield, subrule in rule["cards"].items():
                        lines.append(format_field_line(subfield, subrule, indent + 1))
                elif isinstance(rule, dict) and any(isinstance(v, dict) for v in rule.values()):
                    lines.append("  " * indent + f'- "{field_name}": Object with subfields:')
                    lines.extend(parse_fields(rule, indent + 1))
                else:
                    lines.append(format_field_line(field_name, rule, indent))
            return lines

        lines = parse_fields(fields)

        prompt = f"""🎯 Please generate campaign data for {platform} in the following JSON structure.

    Ensure all required fields are included and each field respects the max_chars / max_count constraints.
    Values must be realistic, persuasive Japanese ad copy derived from product context or user input.

    Format summary:
    {chr(10).join(lines)}

    Return only a valid JSON object with the following keys:
    {{
    "data": {{ ... }},
    "missing_fields": [ "フィード広告・見出し", "カルーセル広告.cards[1].URL" ]
    }}

    If any field is incomplete or missing, leave its value as "" and include the full path in missing_fields.
    """
        return prompt


    async def _write_excel_file_meta(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "Meta広告フォーマット"

        light_pink_fill = PatternFill(start_color="FCE4EC", end_color="FCE4EC", fill_type="solid")
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        headers = ["キャンペーン名", "キャンペーンの目的", "広告セット名", "配信条件／配信ポイント", "性別", "年齢",
                   "エリア", "配置場所"]

        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        data = campaign_data.get('data', {})

        non_list_fields = ["キャンペーン名", "キャンペーンの目的", "性別", "年齢", "エリア", "配置場所"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)

        list_fields = {"広告セット名": 2, "配信条件／配信ポイント": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Row 4: リンク先
        set_cell(ws, 4, 1, "リンク先", fill=light_blue_fill)
        set_cell(ws, 4, 2, "", merge_end_col=8)

        feed_ad = get_field_value(data.get("フィード広告・ストーリー広告"), {})
        feed_ad_url = get_field_value(feed_ad.get("URL", ""))

        # Row 6–9: フィード広告・ストーリー広告
        set_cell(ws, 6, 1, "フィード広告・ストーリー広告 Facebook / instagram", fill=light_blue_fill, merge_end_col=4)
        set_cell(ws, 6, 5, "文字数")

        feed_ad_headline = get_field_value(feed_ad.get("見出し", ""))
        set_cell(ws, 7, 1, "見出し（広告のみ）\n最大50文字")
        set_cell(ws, 7, 2, feed_ad_headline, merge_end_col=4)
        set_cell(ws, 7, 5, str(len(feed_ad_headline)))

        feed_ad_text = get_field_value(feed_ad.get("テキスト", ""))
        set_cell(ws, 8, 1, "テキスト（最大250文字）")
        set_cell(ws, 8, 2, feed_ad_text, merge_end_col=4)
        set_cell(ws, 8, 5, str(len(feed_ad_text)))

        set_cell(ws, 9, 1, "URL")
        set_cell(ws, 9, 2, feed_ad_url, merge_end_col=5)

        # カルーセル広告
        start_row = 10
        max_cards = output_format['fields']['カルーセル広告']['max_count']
        cards = get_field_value(data.get("カルーセル広告", {}), {})
        if "cards" in cards:
            cards = cards.get("cards", [])

        for i in range(max_cards):
            base_row = start_row + i * 5
            card_number = str(i + 1)

            set_cell(ws, base_row, 1, "カルーセル広告 Facebook / instagram", fill=light_blue_fill, merge_end_col=4)
            set_cell(ws, base_row, 5, "文字数", fill=light_blue_fill)

            set_cell(ws, base_row + 1, 1, card_number, fill=light_pink_fill, merge_end_col=5)

            card = cards[i] if i < len(cards) else {}
            card = {k: get_field_value(v) for k, v in card.items()} if isinstance(card, dict) else {"見出し": "",
                                                                                                    "説明文": "",
                                                                                                    "URL": ""}

            headline = card.get("見出し", "")
            set_cell(ws, base_row + 2, 1, "見出し（最大80文字）")
            set_cell(ws, base_row + 2, 2, headline, merge_end_col=4)
            set_cell(ws, base_row + 2, 5, str(len(headline)))

            description = card.get("説明文", "")
            set_cell(ws, base_row + 3, 1, "説明文（最大40文字）")
            set_cell(ws, base_row + 3, 2, description, merge_end_col=4)
            set_cell(ws, base_row + 3, 5, str(len(description)))

            url = card.get("URL", "")
            set_cell(ws, base_row + 4, 1, "URL")
            set_cell(ws, base_row + 4, 2, url, merge_end_col=5)

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 3

        wb.save(file_path)

    async def _write_excel_file_line(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "LINE広告フォーマット"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Define headers
        headers = ["キャンペーン", "グループ", "配信条件／配信ポイント", "年齢", "性別", "エリア"]

        # Write headers
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})

        # Non-list fields
        for idx, header in enumerate(headers, start=1):
            value = get_field_value(data.get(header, ""))
            set_cell(ws, 2, idx, value)


        # Row 4: リンク先
        set_cell(ws, 3, 1, "リンク先", fill=light_blue_fill)
        set_cell(ws, 3, 2, "", merge_end_col=6)

        # Row 6–8: 広告文
        set_cell(ws, 6, 1, "広告文", fill=light_blue_fill, merge_end_col=5)
        set_cell(ws, 6, 6, "文字数", fill=light_blue_fill)

        ad_headline = get_field_value(data.get("見出し", ""))
        set_cell(ws, 7, 1, "見出し（最大20文字）")
        set_cell(ws, 7, 2, ad_headline, merge_end_col=5)
        set_cell(ws, 7, 6, str(len(ad_headline)))

        ad_description = get_field_value(data.get("説明文", ""))
        set_cell(ws, 8, 1, "説明文（最大75文字）")
        set_cell(ws, 8, 2, ad_description, merge_end_col=5)
        set_cell(ws, 8, 6, str(len(ad_description)))

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_youtube(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "YouTube広告フォーマット"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Define headers
        headers = ["キャンペーン名", "広告グループ名", "配信条件", "デバイス", "性別", "年齢", "エリア", "入札設定",
                   "動画設定"]

        # Write headers
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})

        # Non-list fields
        non_list_fields = ["キャンペーン名", "デバイス", "性別", "年齢", "エリア", "入札設定", "動画設定"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)

        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Row 4: 動画リンク先
        set_cell(ws, 4, 1, "動画リンク先", fill=light_blue_fill)
        set_cell(ws, 4, 2, "", merge_end_col=9)

        # Row 6–9: 広告種類
        set_cell(ws, 6, 1, "広告種類", fill=light_blue_fill)
        set_cell(ws, 6, 2, "広告見出し", fill=light_blue_fill)
        set_cell(ws, 6, 3, "説明文", merge_end_col=7, fill=light_blue_fill)

        ad_type = get_field_value(data.get("広告種類", "スキップ可能なインストリーム広告"))
        set_cell(ws, 7, 1, ad_type, merge_end_row=8)

        ad_headline = get_field_value(data.get("広告見出し", ""))
        set_cell(ws, 7, 2, ad_headline, merge_end_row=8)

        ad_descriptions = get_field_value(data.get("説明文", ""))
        for idx, ad_description in enumerate(ad_descriptions, start=7):
            set_cell(ws, idx, 3, ad_description, merge_end_col=7)

        # Row 10: 動画URL & 配信先URL
        set_cell(ws, 9, 2, "動画URL", fill=light_blue_fill)
        set_cell(ws, 9, 3, "", merge_end_col=7)

        set_cell(ws, 10, 2, "配信先URL", fill=light_blue_fill)
        set_cell(ws, 10, 3, "", merge_end_col=7)

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_pmax(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "P-Max広告フォーマット"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Define headers
        headers = ["媒体", "キャンペーン名", "アセットグループ名", "オーディエンスシグナル", "デバイス", "性別", "年齢",
                   "エリア"]

        # Write headers
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})

        for idx, header in enumerate(headers, start=1):
            ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
            value = get_field_value(data.get(header, "P-Max" if header == "媒体" else ""))
            set_cell(ws, 2, idx, value)

        # Row 4: リンク先
        set_cell(ws, 4, 1, "リンク先", fill=light_blue_fill)
        set_cell(ws, 4, 2, "", merge_end_col=8)

        # Row 7: 広告種類
        set_cell(ws, 7, 1, "広告種類", fill=light_blue_fill)
        set_cell(ws, 7, 2, "配信内容", fill=light_blue_fill)
        set_cell(ws, 7, 3, "広告文", fill=light_blue_fill, merge_end_col=7)
        set_cell(ws, 7, 8, "文字数", fill=light_blue_fill)

        # Row 8: 会社名
        company_name = get_field_value(data.get("会社名", ""))
        set_cell(ws, 8, 2, "会社名（最大12文字）")
        set_cell(ws, 8, 3, company_name, merge_end_col=7)
        set_cell(ws, 8, 8, str(len(company_name)))

        # Rows 9–23: 広告見出し (up to 15 short headlines)
        short_headlines = get_field_value(data.get("広告見出し", []))
        base_row = 9
        for i in range(len(short_headlines)):
            row = base_row + i
            headline = short_headlines[i] if i < len(short_headlines) else ""
            label = "広告見出し （最大15文字）" + ("【任意】" if i >= 1 else "")
            set_cell(ws, row, 2, label)
            set_cell(ws, row, 3, headline, merge_end_col=7)
            set_cell(ws, row, 8, str(len(headline)))

        # Rows 24–28: 長い広告見出し (up to 5 long headlines)
        long_headlines = get_field_value(data.get("長い広告見出し", []))
        base_row += len(short_headlines)
        for i in range(len(long_headlines)):
            row = base_row + i
            headline = long_headlines[i] if i < len(long_headlines) else ""
            label = "長い広告見出し （最大45文字）" + ("【任意】" if i >= 1 else "")
            set_cell(ws, row, 2, label)
            set_cell(ws, row, 3, headline, merge_end_col=7)
            set_cell(ws, row, 8, str(len(headline)))

        # Rows 29–33: 説明文 (1 short description + up to 4 long descriptions)
        descriptions = get_field_value(data.get("説明文", []))
        base_row += len(long_headlines)
        short_desc = descriptions[0] if len(descriptions) > 0 else ""
        set_cell(ws, base_row, 2, "説明文（最大30文字）")
        set_cell(ws, base_row, 3, short_desc, merge_end_col=7)
        set_cell(ws, base_row, 8, str(len(short_desc)))

        long_descs = descriptions[1:] if len(descriptions) > 1 else []
        for i in range(len(long_descs)):
            row = base_row + 1 + i
            desc = long_descs[i] if i < len(long_descs) else ""
            set_cell(ws, row, 2, "説明文（最大45文字）【任意】")
            set_cell(ws, row, 3, desc, merge_end_col=7)
            set_cell(ws, row, 8, str(len(desc)))

        base_row += len(long_descs)
        set_cell(ws, 8, 1, "P-Max", merge_end_row=base_row)
        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_google_search(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "検索広告フォーマット"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Table 1: Main campaign details
        table1_headers = ["媒体", "キャンペーン", "広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ",
                          "広告"]
        for idx, header in enumerate(table1_headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})
        platform = get_field_value(data.get("媒体", "Google"))  # Default to Google if not specified

        # Table 1: Non-list fields
        non_list_fields = ["媒体", "キャンペーン"]
        for idx, header in enumerate(table1_headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = get_field_value(data.get(header, "Google" if header == "媒体" else ""))
                if isinstance(value, list):
                    value = value[0] if value else ""
                set_cell(ws, 2, idx, value)

        # Table 1: List fields (広告グループ, メインキーワード, 掛合わせ1, マッチタイプ, 広告)
        list_fields = ["広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ", "広告"]
        max_items = max([len(get_field_value(data.get(field, []))) for field in list_fields], default=0)
        max_items = min(max_items, 2)  # Limit to 2 rows
        for idx, header in enumerate(table1_headers, start=1):
            if header in list_fields:
                values = get_field_value(data.get(header, []))
                for row, value in enumerate(values[:max_items], start=2):
                    set_cell(ws, row, idx, value)

        # Table 2: Ad details
        base_row = 1
        table2_headers = ["広告名", "見出し(30字以内)", "固定", "文字数", "説明文(90字以内)", "固定", "文字数", "パス",
                          "文字数"]
        for idx, header in enumerate(table2_headers, start=10):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Headlines (up to 15)
        headlines = get_field_value(data.get("見出し", []))
        current_row = base_row + 1
        for idx, headline in enumerate(headlines[:15]):  # Limit to 15 headlines
            set_cell(ws, current_row + idx, 11, headline)
            set_cell(ws, current_row + idx, 13, str(len(headline)))

        # Descriptions (up to 4)
        descriptions = get_field_value(data.get("説明文", []))
        for idx, description in enumerate(descriptions[:4]):  # Limit to 4 descriptions
            row = current_row + idx
            set_cell(ws, row, 14, description)
            set_cell(ws, row, 16, str(len(description)))

        # Paths (up to 2)
        paths = get_field_value(data.get("パス", []))
        for idx, path in enumerate(paths[:2]):  # Limit to 2 paths
            row = current_row + idx
            set_cell(ws, row, 17, path)
            set_cell(ws, row, 18, str(len(path)))

        # Table 3: URL and additional info
        table3_row = max(len(headlines[:15]), len(descriptions[:4]), len(paths[:2])) + 1
        set_cell(ws, table3_row, 10, "URL")
        url_value = get_field_value(data.get("入稿先URL", ""))
        if platform == "Google":
            set_cell(ws, table3_row + 1, 10, "入稿先URL:Google")
            set_cell(ws, table3_row + 1, 11, url_value + "?utm_source=google&utm_medium=cpc")
        elif platform == "Yahoo":
            set_cell(ws, table3_row + 1, 10, "入稿先URL:Yahoo")
            set_cell(ws, table3_row + 1, 11, url_value + "?utm_source=yahoo&utm_medium=cpc")
        else:
            raise ValueError("Invalid platform specified. Must be 'Google' or 'Yahoo'.")

        # Additional info section
        additional_fields = ["地域", "性別（googleのみ）", "年齢（googleのみ）", "年収（googleのみ）", "ターゲティング",
                             "備考"]
        additional_row = 2 + max_items + 2
        set_cell(ws, additional_row, 1, "＊このキャンペーンの注意事項", fill=light_blue_fill, merge_end_col=7)

        for idx, field in enumerate(additional_fields):
            row = additional_row + 1 + idx
            field_key = field.replace("（googleのみ）", "")  # Extract key without "(googleのみ)"
            value = ""
            if "googleのみ" in field and platform != "Google":
                value = ""  # Keep empty for Yahoo
            else:
                value = get_field_value(data.get(field_key, ""))
            set_cell(ws, row, 1, field)
            set_cell(ws, row, 2, value, merge_end_col=7)

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_google_demand_gen(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "Googleデマンドジェネレーション"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Define headers
        headers = ["媒体", "キャンペーン名", "広告グループ名", "配信条件", "デバイス", "性別", "年齢", "エリア"]

        # Write headers
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})

        # Non-list fields
        non_list_fields = ["媒体", "キャンペーン名", "デバイス", "性別", "年齢", "エリア"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)

        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Row 4: リンク先
        set_cell(ws, 4, 1, "リンク先", fill=light_blue_fill)
        set_cell(ws, 4, 2, "", merge_end_col=8)

        # Row 5: リンクURL
        set_cell(ws, 5, 1, "Google　URL", fill=light_blue_fill)
        link_url = get_field_value(data.get("Google　URL", "?utm_source=google&utm_medium=display"))
        set_cell(ws, 5, 2, link_url, merge_end_col=8)

        # Row 6: 広告文 headers
        set_cell(ws, 7, 1, "広告種類", fill=light_blue_fill)
        set_cell(ws, 7, 2, "配信内容", fill=light_blue_fill)
        set_cell(ws, 7, 3, "広告文", merge_end_col=7, fill=light_blue_fill)
        set_cell(ws, 7, 8, "文字数", fill=light_blue_fill)

        # Row 7: 広告種類
        ad_type = get_field_value(data.get("広告種類", "レスポンシブ ディスプレイ広告"))

        # 配信内容 and 広告文
        content_configs = [
            {"key": "主体者表記", "label_base": "主体者表記（最大25文字）", "max_chars": 25, "is_list": False},
            {"key": "広告見出し 短縮", "label_base": "広告見出し 短縮（最大40文字）", "max_chars": 40, "is_list": True},
            {"key": "説明文", "label_base": "説明文（最大90文字）", "max_chars": 90, "is_list": True}
        ]

        row = 8
        total_rows = 8  # Track total rows for merging 広告種類
        for config in content_configs:
            key = config["key"]
            label_base = config["label_base"]
            is_list = config["is_list"]

            values = get_field_value(data.get(key, [] if is_list else ""))
            if not is_list:
                values = [values]

            for idx, value in enumerate(values):
                label = label_base if idx == 0 else f"{label_base}【任意】"
                set_cell(ws, row, 2, label)
                set_cell(ws, row, 3, value, merge_end_col=7)
                set_cell(ws, row, 8, len(value) if value else 0)
                row += 1
                total_rows += 1

        # Set 広告種類 after calculating total rows
        set_cell(ws, 8, 1, ad_type, merge_end_row=total_rows - 1)

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_google_display(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "Googleディスプレイ広告"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Define headers
        headers = ["媒体", "キャンペーン名", "広告グループ名", "配信条件", "デバイス", "性別", "年齢", "エリア",
                   "除外プレースメント"]

        # Write headers
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=light_blue_fill, bold=True)

        # Get campaign data
        data = campaign_data.get('data', {})

        # Write non-list fields
        non_list_fields = ["媒体", "キャンペーン名", "デバイス", "性別", "年齢", "エリア", "除外プレースメント"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = get_field_value(data.get(header, "Googleディスプレイ" if header == "媒体" else ""))
                set_cell(ws, 2, idx, value)

        # Write list fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Row 4: リンク先
        set_cell(ws, 4, 1, "リンク先", fill=light_blue_fill)
        set_cell(ws, 4, 2, "", merge_end_col=9)

        # Get platform
        platform = get_field_value(data.get("媒体", data.get("platform_selected", "Googleディスプレイ")))

        # Row 5: URL based on platform
        base_row = 5
        row = base_row
        if platform == "Googleディスプレイ":
            google_url = get_field_value(
                data.get("広告文", {}).get("Google", {}).get("URL", "?utm_source=google&utm_medium=display"))
            set_cell(ws, row, 1, "Google URL", fill=light_blue_fill)
            set_cell(ws, row, 2, google_url, merge_end_col=9)
            row += 1
        elif platform == "Yahooディスプレイ":
            yda_url = get_field_value(
                data.get("広告文", {}).get("Yahoo", {}).get("URL", "?utm_source=yahoo&utm_medium=display"))
            set_cell(ws, row, 1, "YDA URL", fill=light_blue_fill)
            set_cell(ws, row, 2, yda_url, merge_end_col=9)
            row += 1
        else:
            raise ValueError("Invalid platform specified. Must be 'Googleディスプレイ' or 'Yahooディスプレイ'.")

        # Skip a row
        row += 1

        # 広告文 based on platform
        if platform == "Yahooディスプレイ":
            # Yahooディスプレイ広告文
            set_cell(ws, row, 1, "広告文（Yahooディスプレイ）")
            set_cell(ws, row + 1, 1, "広告種類", fill=light_blue_fill)
            set_cell(ws, row + 1, 2, "配信内容", fill=light_blue_fill)
            set_cell(ws, row + 1, 3, "広告文", merge_end_col=7, fill=light_blue_fill)
            set_cell(ws, row + 1, 8, "文字数", fill=light_blue_fill)
            row += 2

            # Yahoo テキスト広告
            yahoo_text = data.get("広告文", {}).get("Yahoo", {}).get("テキスト広告", {})
            text_ad_configs = [
                {"key": "タイトル", "label": "タイトル（最大30文字）", "max_chars": 30},
                {"key": "説明文1", "label": "説明文１（最大38文字）", "max_chars": 38},
                {"key": "説明文2", "label": "説明文２（最大38文字）", "max_chars": 38}
            ]

            for config in text_ad_configs:
                value = get_field_value(yahoo_text.get(config["key"], ""))
                set_cell(ws, row, 2, config["label"])
                set_cell(ws, row, 3, value, merge_end_col=7)
                set_cell(ws, row, 8, len(value) if value else 0)
                row += 1
            set_cell(ws, base_row + 4, 1, "テキスト広告", merge_end_row=row-1)

            # Yahoo レスポンシブ広告
            yahoo_resp = data.get("広告文", {}).get("Yahoo", {}).get("レスポンシブ広告", {})
            resp_ad_configs = [
                {"key": "主体者表記", "label": "主体者表記（最大40文字）", "max_chars": 40, "is_list": False},
                {"key": "タイトル", "label_base": "タイトル{num} 通常（最大40文字）", "max_chars": 40, "is_list": True},
                {"key": "説明文", "label_base": "説明文{num}（最大90文字）", "max_chars": 90, "is_list": True}
            ]

            for config in resp_ad_configs:
                values = get_field_value(yahoo_resp.get(config["key"], [] if config["is_list"] else ""))
                if not config["is_list"]:
                    values = [values]
                for idx, value in enumerate(values):
                    label = config["label"] if "label" in config else config["label_base"].format(num=idx + 1)
                    set_cell(ws, row, 2, label)
                    set_cell(ws, row, 3, value, merge_end_col=7)
                    set_cell(ws, row, 8, len(value) if value else 0)
                    row += 1
            set_cell(ws, base_row + 4 + len(text_ad_configs), 1, "レスポンシブ広告", merge_end_row=row-1)

        elif platform == "Googleディスプレイ":
            # Googleディスプレイ広告文
            set_cell(ws, row, 1, "広告文（Googleディスプレイ）")
            set_cell(ws, row + 1, 1, "広告種類", fill=light_blue_fill)
            set_cell(ws, row + 1, 2, "配信内容", fill=light_blue_fill)
            set_cell(ws, row + 1, 3, "広告文", merge_end_col=7, fill=light_blue_fill)
            set_cell(ws, row + 1, 8, "文字数", fill=light_blue_fill)
            row += 2

            google_ad = data.get("広告文", {}).get("Google", {})
            google_ad_configs = [
                {"key": "主体者表記", "label": "主体者表記（最大25文字）", "max_chars": 25, "is_list": False},
                {"key": "広告見出し", "label": "広告見出し 通常（最大90文字）", "max_chars": 90, "is_list": False},
                {"key": "広告見出し 短縮", "label_base": "広告見出し 短縮（最大30文字）", "max_chars": 30,
                 "is_list": True},
                {"key": "説明文", "label_base": "説明文（最大90文字）", "max_chars": 90, "is_list": True}
            ]

            for config in google_ad_configs:
                values = get_field_value(google_ad.get(config["key"], [] if config["is_list"] else ""))
                if not config["is_list"]:
                    values = [values]
                for idx, value in enumerate(values):
                    label = config["label"] if "label" in config else config["label_base"] + (
                        "【任意】" if idx > 0 else "")
                    set_cell(ws, row, 2, label)
                    set_cell(ws, row, 3, value, merge_end_col=7)
                    set_cell(ws, row, 8, len(value) if value else 0)
                    row += 1
            set_cell(ws, base_row + 4, 1, "レスポンシブ ディスプレイ広告", merge_end_row=row-1)

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _write_excel_file_ad_extensions(self, campaign_data, output_format, file_path):
        wb = Workbook()
        ws = wb.active
        ws.title = "広告拡張フォーマット"

        # Define fill colors
        light_blue_fill = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")

        # Get campaign data
        data = campaign_data.get('data', {})
        platform = get_field_value(data.get("platform_selected", "Google"))

        # Row counter
        row = 1

        # サイトリンク
        set_cell(ws, row, 1, "◆サイトリンク／クイックリンク", fill=light_blue_fill, bold=True)
        set_cell(ws, row + 1, 1, "※広告とは別にリンク先を付与できます", fill=light_blue_fill)
        set_cell(ws, row + 2, 1, "見出し（最大半角25字）", fill=light_blue_fill)
        set_cell(ws, row + 2, 2, "説明文（最大半角35字）", fill=light_blue_fill)
        set_cell(ws, row + 2, 3, "URL", fill=light_blue_fill)
        set_cell(ws, row + 2, 4, "見出し", fill=light_blue_fill)
        set_cell(ws, row + 2, 5, "説明文", fill=light_blue_fill)
        row += 3

        sitelinks = get_field_value(data.get("サイトリンク", []))
        for idx, sitelink in enumerate(sitelinks[:6]):  # Limit to 6 sitelinks
            sitelink = sitelink if isinstance(sitelink, dict) else {}
            headline = get_field_value(sitelink.get("見出し", ""))
            desc1 = get_field_value(sitelink.get("説明文1", ""))
            desc2 = get_field_value(sitelink.get("説明文2", ""))
            url = get_field_value(sitelink.get("URL", ""))
            set_cell(ws, row, 1, f"{idx + 1}")
            set_cell(ws, row, 2, headline)
            set_cell(ws, row, 3, desc1)
            set_cell(ws, row, 4, url)
            set_cell(ws, row, 5, str(len(headline)))
            set_cell(ws, row + 1, 3, desc2)
            set_cell(ws, row + 1, 5, str(len(desc2)))
            row += 2

        # コールアウト
        row += 1
        set_cell(ws, row, 1, "◆コールアウト／テキスト補足", fill=light_blue_fill, bold=True)
        set_cell(ws, row + 1, 1, "※広告文に補足のテキストを付与できます", fill=light_blue_fill)
        set_cell(ws, row + 2, 1, "テキスト（最大半角25字）", fill=light_blue_fill)
        set_cell(ws, row + 2, 2, "文字数", fill=light_blue_fill)
        row += 3

        callouts = get_field_value(data.get("コールアウト", []))
        for idx, callout in enumerate(callouts[:6]):  # Limit to 6 callouts
            set_cell(ws, row, 1, f"{idx + 1}")
            set_cell(ws, row, 2, callout)
            set_cell(ws, row, 3, str(len(callout)))
            row += 1

        # 構造化スニペット
        row += 1
        set_cell(ws, row, 1, "◆構造化スニペット／カテゴリ補足", fill=light_blue_fill, bold=True)
        set_cell(ws, row + 1, 1,
                 "※商材の詳細情報を記載することが可能となります。また、構造化スニペットは、審査落ちが多いためテキストはお任せいただきたいです。",
                 fill=light_blue_fill)
        set_cell(ws, row + 2, 1, "ヘッダー(見出し)", fill=light_blue_fill)
        set_cell(ws, row + 2, 2, "テキスト(最大半角25字)", fill=light_blue_fill)
        set_cell(ws, row + 2, 3, "文字数", fill=light_blue_fill)
        row += 3

        snippet = data.get("構造化スニペット", {})
        header = get_field_value(snippet.get("ヘッダー", "スタイル"))
        values = get_field_value(snippet.get("値", []))
        set_cell(ws, row, 1, "1")
        set_cell(ws, row, 2, header)
        set_cell(ws, row, 3, str(len(header)))
        row += 1
        for idx, value in enumerate(values[:10]):  # Limit to 10 values
            set_cell(ws, row, 1, f"{idx + 2}")
            set_cell(ws, row, 2, value)
            set_cell(ws, row, 3, str(len(value)))
            row += 1

        # 電話番号表示
        row += 1
        set_cell(ws, row, 1, "◆電話番号表示", fill=light_blue_fill, bold=True)
        set_cell(ws, row + 1, 1, "※広告表示の際に電話番号を付与することが可能", fill=light_blue_fill)
        set_cell(ws, row + 2, 1, "電話番号", fill=light_blue_fill)
        row += 3
        phone = get_field_value(data.get("電話番号表示", ""))
        set_cell(ws, row, 1, phone)
        row += 1

        # 価格表示オプション (Google only)
        row += 1
        set_cell(ws, row, 1, "◆価格表示オプション（Googleのみ）", fill=light_blue_fill, bold=True)
        set_cell(ws, row + 1, 1, "※価格表示の詳細を記載することが可能", fill=light_blue_fill)
        set_cell(ws, row + 2, 1, "価格表示オプション", fill=light_blue_fill)
        set_cell(ws, row + 2, 2, "文字数", fill=light_blue_fill)
        row += 3

        if platform == "Google":
            price_extensions = get_field_value(data.get("価格表示オプション", []))
            for idx, price in enumerate(price_extensions[:3]):  # Limit to 3 price extensions
                price = price if isinstance(price, dict) else {}
                header = get_field_value(price.get("ヘッダー", ""))
                desc = get_field_value(price.get("説明文", ""))
                price_value = get_field_value(price.get("価格", ""))
                unit = get_field_value(price.get("単位", "単位なし"))
                url = get_field_value(price.get("URL", ""))
                set_cell(ws, row, 1, f"{idx + 1}")
                set_cell(ws, row, 2, f"ヘッダー（最大半角25字）")
                set_cell(ws, row, 3, header)
                set_cell(ws, row, 4, str(len(header)))
                set_cell(ws, row + 1, 2, "価格")
                set_cell(ws, row + 1, 3, price_value)
                set_cell(ws, row + 1, 4, unit)
                set_cell(ws, row + 2, 2, "説明文（最大半角25字）")
                set_cell(ws, row + 2, 3, desc)
                set_cell(ws, row + 2, 4, str(len(desc)))
                set_cell(ws, row + 3, 2, "URL")
                set_cell(ws, row + 3, 3, url)
                set_cell(ws, row + 3, 4, str(len(url)))
                row += 4
        else:  # Yahoo: Keep headers but empty content
            for idx in range(3):  # Still create headers for 3 price extensions
                set_cell(ws, row, 1, f"{idx + 1}")
                set_cell(ws, row, 2, "ヘッダー（最大半角25字）")
                set_cell(ws, row, 3, "")
                set_cell(ws, row, 4, "0")
                set_cell(ws, row + 1, 2, "価格")
                set_cell(ws, row + 1, 3, "")
                set_cell(ws, row + 1, 4, "単位なし")
                set_cell(ws, row + 2, 2, "説明文（最大半角25字）")
                set_cell(ws, row + 2, 3, "")
                set_cell(ws, row + 2, 4, "0")
                set_cell(ws, row + 3, 2, "URL")
                set_cell(ws, row + 3, 3, "")
                set_cell(ws, row + 3, 4, "0")
                row += 4

        # プロモーション (Google only)
        row += 1
        set_cell(ws, row, 1, "◆プロモーション（Googleのみ）", fill=light_blue_fill, bold=True)
        row += 1
        promotion = data.get("プロモーション", {}) if platform == "Google" else {}
        promotion_fields = [
            {"key": "年間行事", "label": "年間行事", "default": "なし"},
            {"key": "プロモーションタイプ", "label": "プロモーションタイプ", "suffix": "割引額 ￥"},
            {"key": "アイテム", "label": "アイテム", "default": ""},
            {"key": "最終ページURL", "label": "最終ページURL", "default": ""},
            {"key": "プロモーションの詳細", "label": "プロモーションの詳細", "default": "なし"},
            {"key": "開始日", "label": "開始日", "default": ""},
            {"key": "終了日", "label": "終了日", "default": ""}
        ]
        for field in promotion_fields:
            value = get_field_value(promotion.get(field["key"], field.get("default", "")))
            if platform != "Google" and field["key"] != "年間行事":  # Keep 年間行事 but empty others for Yahoo
                value = ""
            set_cell(ws, row, 1, field["label"])
            set_cell(ws, row, 2, value + (f" {field['suffix']}" if "suffix" in field else ""))
            row += 1

        # Schedule details
        schedule = promotion.get("スケジュールの詳細", {}) if platform == "Google" else {}
        schedule_fields = [
            {"key": "開始日", "label": "開始日", "default": ""},
            {"key": "終了日", "label": "終了日", "default": ""},
            {"key": "曜日と時間帯", "label": "曜日と時間帯", "default": ""}
        ]
        set_cell(ws, row, 1, "スケジュールの詳細")
        row += 1
        for field in schedule_fields:
            value = get_field_value(schedule.get(field["key"], field.get("default", "")))
            if platform != "Google":
                value = ""
            set_cell(ws, row, 1, field["label"])
            set_cell(ws, row, 2, value)
            row += 1

        # Auto width (approximate)
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2

        wb.save(file_path)

    async def _load_output_format(self, format_file: str) -> dict:
        import json
        import aiofiles
        async with aiofiles.open(self.outputs_dir / format_file, mode='r', encoding='utf-8') as f:
            return json.loads(await f.read())



    @staticmethod
    def should_update_summary(chat_history: list) -> bool:
        return len(chat_history) == 0 or len(chat_history) % 20 == 0

    @staticmethod
    def extract_questions(chat_history: list, current_question: str) -> list[str]:
        if not chat_history:
            return [current_question]
        questions = [msg.content for i, msg in enumerate(chat_history) if i % 2 == 0]
        questions.append(current_question)
        return questions
